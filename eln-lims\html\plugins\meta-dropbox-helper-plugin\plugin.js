/*
 * Interesting helper classes to look at
 * js/util/FormUtil.js
 * js/util/Util.js
 * js/server/ServerFacade.js
 */

var customDialogHTML = `
<div id="customDialog" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: white; padding: 20px; box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5); z-index: 9999;">
  <h4 style="margin-bottom: 20px;">Meta DropBox Upload Helper</h4>
  <label for="target" style="width: 150px; display: inline-block;">Entity:</label>
  <input type="text" id="target" placeholder="OBJECT" readonly style="margin-left: 10px;">
  <br>
  <label for="path" style="width: 150px; display: inline-block;">Path:</label>
  <input type="text" id="path" readonly style="margin-left: 10px;">
  <br>
  <label for="permId" style="width: 150px; display: inline-block;">Perm ID:</label>
  <input type="text" id="permId" readonly style="margin-left: 10px;">
  <br>
  <label for="data" style="width: 150px; display: inline-block;">Dataset name:</label>
  <input type="text" id="data" style="margin-left: 10px;">
  <br>
  <br>
  <div style="text-align: center;">
    <button onclick="onSubmit()">Download JSON</button>
    <button onclick="closeCustomDialog()">Close</button>
  </div>
</div>`;

var dialogInitialized = false;
var dialog;

function showCustomDialog() {
  if (!dialogInitialized) {
    // Create the dialog only if it hasn't been initialized yet
    var tempElement = document.createElement('div');
    tempElement.innerHTML = customDialogHTML.trim();

    dialog = tempElement.firstChild;
    document.body.appendChild(dialog);
    dialogInitialized = true;
  }

  // Display the dialog
  dialog.style.display = 'block';

  var objectPath = document.getElementById('path');
  var objectPathInfo = objectPath ? objectPath.innerText : 'No path available';

  var objectCode = objectPathInfo.split('/');
  var objectCodeInfo = objectCode[objectCode.length -1];

  var permId = document.getElementById('sampleID');
  var permIdInfo = permId ? permId.innerText : 'No permID available';

  var pathField = dialog.querySelector('#path');
  pathField.value = objectPathInfo;

  var permField = document.getElementById('permId');
  permField.value = permIdInfo;


}
function closeCustomDialog() {
  if (dialog) {
    // Hide the dialog
    dialog.style.display = 'none';
    var dataField = dialog.querySelector('#data');
    dataField.value = '';
  }
}

function onSubmit() {
  var permId = document.getElementById('permId').value;
  var dataName = document.getElementById('data').value;
  var path = document.getElementById('path').innerText;

  // Create the JSON structure
  var jsonData = {
    TARGET: {
      ENTITY: "OBJECT",
      PATH: path,
      ENTITYCODE: permId,
      CREATE: "0"
    },
    DATASET: {
      NAME: dataName,
      TYPE: "ATTACHMENT",
      PROPERTIES: {}
    }
  };

  // Convert the object to JSON
  var jsonString = JSON.stringify(jsonData, null, 2);

  // Create a Blob and create a URL for the blob
  var blob = new Blob([jsonString], { type: 'application/json' });
  var url = URL.createObjectURL(blob);

  // Create a link element and trigger a click to download the JSON file
  var a = document.createElement('a');
  a.href = url;
  a.download = 'data.json';
  a.click();

  // Clean up by revoking the URL
  URL.revokeObjectURL(url);
}


function WorkshopTechnology() {
	this.init();
}

$.extend(WorkshopTechnology.prototype, ELNLIMSPlugin.prototype, {
	init: function() {

	},
	forcedDisableRTF : [],
	forceMonospaceFont : [],
       	sampleTypeDefinitionsExtension : {
            
	},

	dataSetTypeDefinitionsExtension : {

	},
	
	sampleFormTop: function ($container, model) {
        	var toolbarModel = [];
        	var $jsonHelperButton = FormUtil.getButtonWithText("Meta-Dropbox Helper", function () {
            		showCustomDialog();
        	}, '', null);
        	toolbarModel.push({ component: $jsonHelperButton, tooltip: "Meta-Dropbox Helper" });
        	toolbarModel.forEach(function (item) {
        		$container.append(item.component);
    		});
    	},
	// experimentFormTop: function($container, model) {
  //   		var toolbarModel = [];
  //       	var $jsonHelperButton = FormUtil.getButtonWithText("Meta-Dropbox Helper", function () {
  //           		showCustomDialog();
  //       	}, '', null);
  //       	toolbarModel.push({ component: $jsonHelperButton, tooltip: "Meta-Dropbox Helper" });
  //       	toolbarModel.forEach(function (item) {
  //       		$container.append(item.component);
  //   		});
	// },

	sampleFormBottom : function($container, model) {

	},
	dataSetFormTop : function($container, model) {

	},
	dataSetFormBottom : function($container, model) {

	}
});

profile.plugins.push(new WorkshopTechnology());