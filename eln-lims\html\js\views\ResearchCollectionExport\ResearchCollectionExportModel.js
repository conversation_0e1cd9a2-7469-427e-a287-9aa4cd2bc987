/*
 * Copyright 2011 <PERSON><PERSON>, CISD
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

function ResearchCollectionExportModel() {
    this.submissionTypes = [
        {
            label: "Data Collection",
            value: "/swordv2/collection/20.500.11850/30"
        },
        {
            label: "Dataset",
            value: "/swordv2/collection/20.500.11850/31"
        },
        {
            label: "Image",
            value: "/swordv2/collection/20.500.11850/32"
        },
        {
            label: "Model",
            value: "/swordv2/collection/20.500.11850/33"
        },
        {
            label: "Sound",
            value: "/swordv2/collection/20.500.11850/35"
        },
        {
            label: "Video",
            value: "/swordv2/collection/20.500.11850/36"
        },
        {
            label: "Other Research Data",
            value: "/swordv2/collection/20.500.11850/37"
        }
    ];
}