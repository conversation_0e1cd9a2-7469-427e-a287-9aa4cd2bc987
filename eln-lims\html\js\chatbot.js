define(['jquery', 'openbis'], function($, openbis) {
    var ChatbotManager = {
        init: function() {
            this.injectChatbotHTML();
            this.initializeEventListeners();
            this.sessionId = null;
            this.chatbotApiUrl = '/openbis-chatbot/api/chat';
        },

        injectChatbotHTML: function() {
            const chatbotHTML = `
                <div class="chatbot-trigger">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="chatbot-container">
                    <div class="chatbot-header">
                        <span>openBIS Assistant</span>
                        <i class="fas fa-times" id="close-chatbot"></i>
                    </div>
                    <div class="chatbot-messages"></div>
                    <div class="chatbot-input">
                        <input type="text" placeholder="Type your message...">
                        <button type="submit">Send</button>
                    </div>
                </div>
            `;
            $('body').append(chatbotHTML);
        },

        initializeEventListeners: function() {
            const self = this;
            
            $('.chatbot-trigger').on('click', function() {
                $('.chatbot-container').fadeToggle();
                if (!self.sessionId) {
                    self.initializeSession();
                }
            });

            $('#close-chatbot').on('click', function(e) {
                e.stopPropagation();
                $('.chatbot-container').fadeOut();
            });

            $('.chatbot-input button').on('click', function() {
                self.sendMessage();
            });

            $('.chatbot-input input').on('keypress', function(e) {
                if (e.which === 13) {
                    self.sendMessage();
                }
            });
        },

        initializeSession: function() {
            const self = this;
            $.ajax({
                url: this.chatbotApiUrl,
                method: 'POST',
                data: JSON.stringify({
                    message: '',
                    session_id: null,
                    action: 'init'
                }),
                contentType: 'application/json',
                success: function(response) {
                    self.sessionId = response.session_id;
                    self.addMessage('assistant', 'Hello! How can I help you with openBIS today?');
                },
                error: function() {
                    self.addMessage('assistant', 'Sorry, I\'m having trouble connecting. Please try again later.');
                }
            });
        },

        sendMessage: function() {
            const input = $('.chatbot-input input');
            const message = input.val().trim();
            
            if (!message) return;
            
            input.val('');
            this.addMessage('user', message);
            
            const self = this;
            $.ajax({
                url: this.chatbotApiUrl,
                method: 'POST',
                data: JSON.stringify({
                    message: message,
                    session_id: self.sessionId
                }),
                contentType: 'application/json',
                success: function(response) {
                    self.addMessage('assistant', response.response);
                },
                error: function() {
                    self.addMessage('assistant', 'Sorry, I encountered an error. Please try again.');
                }
            });
        },

        addMessage: function(type, content) {
            const messageHTML = `
                <div class="chatbot-message ${type}">
                    <div class="message-content">${content}</div>
                </div>
            `;
            $('.chatbot-messages').append(messageHTML);
            $('.chatbot-messages').scrollTop($('.chatbot-messages')[0].scrollHeight);
        }
    };

    return ChatbotManager;
});