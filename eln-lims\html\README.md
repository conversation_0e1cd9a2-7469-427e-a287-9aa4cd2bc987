# OpenBIS Chatbot Integration

## Overview
This integration adds a chatbot assistant to the OpenBIS ELN-LIMS interface, providing interactive help and guidance to users.

## Directory Structure
```
eln-lims/html/
├── app.py                    # Flask backend server
├── requirements.txt          # Python dependencies
├── css/
│   └── chatbot.css          # Chatbot styling
├── js/
│   ├── config/
│   │   └── chatbot-config.js # Chatbot configuration
│   └── chatbot-init.js       # Chatbot frontend initialization
```

## Setup Instructions

1. Install Python dependencies:
   ```bash
   cd eln-lims/html
   pip install -r requirements.txt
   ```

2. Start the Flask backend server:
   ```bash
   python app.py
   ```
   The server will run on port 5000 by default.

3. The chatbot interface will automatically initialize when users log into OpenBIS.

## Configuration
The chatbot configuration is managed in `js/config/chatbot-config.js`. You can modify:
- API endpoint
- Server host
- Server port

## Troubleshooting
1. If the chatbot button doesn't appear:
   - Check browser console for JavaScript errors
   - Verify that all CSS and JavaScript files are properly loaded
   - Ensure the Flask server is running

2. If the chatbot can't connect to the backend:
   - Verify the Flask server is running on the correct port
   - Check the API endpoint configuration in chatbot-config.js
   - Ensure there are no CORS issues