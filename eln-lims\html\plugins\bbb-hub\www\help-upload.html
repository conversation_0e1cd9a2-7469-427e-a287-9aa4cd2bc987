<h2>DataSet Upload Workflow</h2>

<h3>1. Installing the DataSet Uploader</h3>
<p><PERSON><PERSON><PERSON><PERSON><PERSON> uses openBIS as front end, so the first step to Upload a DataSet is to install the "openBIS DataSet Uploader" from the <a href="https://wiki-bsse.ethz.ch/display/bis/openBIS+Download+Page">openBIS Downloads Page</a>.</p>

<center><img src="./plugins/bbb-hub/www/help-upload-0.png" width="70%"></center>

<h3>2. Submitting a DataSet</h3>
<p>When first starting the DataSet uploader a login screen is presented, you can use the same credentials you use on the website.</p>

<center><img src="./plugins/bbb-hub/www/help-upload-1.png" width="70%"></center>

<p>When filling the information for the upload you need to:</p>

<p>1. Select the "FOLDER" containing your "EXPERIMENT_DEFINITION" DataSet.</p>
<p>2. Select as owner "/PUBLIC/SUBMISSIONS/SUBMISSION_OWNER".</p>
<p>3. Select as Data Set Type "EXPERIMENT_DEFINITION".</p>

<p>A working example is shown below.</p>

<center><img src="./plugins/bbb-hub/www/help-upload-2.png" width="70%"></center>

<p>Last, press the "UPLOAD" button, this will start the upload of your DataSet and a progress bar will be shown, changing to the message "registered" when finished.</p>

<p>After the registration process is finished, can take a few seconds/minutes you should find the result on the interface under the "INPUT" section as shown below.</p>

<center>
    <img src="./plugins/bbb-hub/www/help-upload-3.png" width="70%">
    <img src="./plugins/bbb-hub/www/help-upload-4.png" width="70%">
</center>

<h3>3. Obtaining the results</h3>

<p>The administrator will automatic receive an email from the system to execute the workflow of your submission after registration.</p>

<p>The results will be registered under the "RESULTS" section of your experiment on the UI when this are available.</p>

<h3>4. Experiment Definition Format</h3>

<p>An example of the format used by the EXPERIMENT_DEFINITION DataSet with dummy fasta files can be found <a href="./plugins/bbb-hub/www/BBBHub_EXPERIMENT_DEFINITION.zip">here</a>.</p>

<center><img src="./plugins/bbb-hub/www/help-upload-5.png" width="40%"></center>