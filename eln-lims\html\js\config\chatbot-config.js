// Chatbot configuration
const ChatbotConfig = {
    apiUrl: '/openbis-chatbot/api/chat',
    serverHost: window.location.protocol + '//' + window.location.hostname,
    serverPort: '5000',
    initializeEndpoint: function() {
        return this.serverHost + ':' + this.serverPort + this.apiUrl;
    }
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatbotConfig;
} else {
    window.ChatbotConfig = ChatbotConfig;
}