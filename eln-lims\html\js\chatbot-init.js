// Chatbot initialization and management
document.addEventListener('DOMContentLoaded', function() {
    // Create and inject chatbot HTML
    const chatbotHTML = `
        <div id="chatbot-trigger" class="chatbot-trigger">
            <i class="fas fa-comments"></i>
        </div>
        <div id="chatbot-container" class="chatbot-container hidden">
            <div class="chatbot-header">
                <h3>OpenBIS Assistant</h3>
                <button id="chatbot-close" class="chatbot-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="chatbot-messages" class="chatbot-messages"></div>
            <div class="chatbot-input-container">
                <textarea id="chatbot-input" placeholder="Type your message here..."></textarea>
                <button id="chatbot-send">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    `;

    // Create container div and inject HTML
    const chatbotWrapper = document.createElement('div');
    chatbotWrapper.id = 'chatbot-wrapper';
    chatbotWrapper.innerHTML = chatbotHTML;
    document.body.appendChild(chatbotWrapper);

    // Get DOM elements
    const trigger = document.getElementById('chatbot-trigger');
    const container = document.getElementById('chatbot-container');
    const closeBtn = document.getElementById('chatbot-close');
    const input = document.getElementById('chatbot-input');
    const sendBtn = document.getElementById('chatbot-send');
    const messagesContainer = document.getElementById('chatbot-messages');

    // Initialize session
    let sessionId = localStorage.getItem('chatbot-session-id');
    if (!sessionId) {
        sessionId = 'session_' + Date.now();
        localStorage.setItem('chatbot-session-id', sessionId);
    }

    // Event Listeners
    trigger.addEventListener('click', function() {
        container.classList.toggle('hidden');
        trigger.classList.toggle('hidden');
    });

    closeBtn.addEventListener('click', function() {
        container.classList.add('hidden');
        trigger.classList.remove('hidden');
    });

    // Handle message sending
    function sendMessage() {
        const message = input.value.trim();
        if (!message) return;

        // Add user message to chat
        addMessage('user', message);
        input.value = '';

        // Send to backend
        fetch(ChatbotConfig.initializeEndpoint(), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: sessionId,
                message: message
            })
        })
        .then(response => response.json())
        .then(data => {
            addMessage('assistant', data.response);
        })
        .catch(error => {
            console.error('Error:', error);
            addMessage('assistant', 'Sorry, there was an error processing your request.');
        });
    }

    // Add message to chat
    function addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chatbot-message ${role}-message`;
        messageDiv.innerHTML = `
            <div class="message-content">
                ${content.replace(/\n/g, '<br>')}
            </div>
        `;
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Send button click
    sendBtn.addEventListener('click', sendMessage);

    // Enter key press (with shift+enter for new line)
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
});