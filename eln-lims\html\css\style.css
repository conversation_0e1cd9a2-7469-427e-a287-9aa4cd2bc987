/*
 * Copyright 2014 <PERSON><PERSON>, Scientific IT Services
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
/*
 * Placeholder Patch
 * https://github.com/select2/select2/issues/291
 */

/* 
 * General Overrides
 */

.select2-container .select2-selection__rendered > *:first-child.select2-search--inline {
	width: 100% !important;
}
.select2-container .select2-selection__rendered > *:first-child.select2-search--inline .select2-search__field {
	width: 100% !important;
}

.form-group {
    margin-bottom: 5px;
}

a:hover {
    text-decoration: none;
    cursor: pointer;
}

.container  {
	width: 100%;
}

body {
	overflow:hidden;

	/*ckeditor 5 z-index*/
    --ck-z-default: 100;
    --ck-z-modal: 200;
}

h1, h2, legend {
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
	font-weight: 400;
	color : black;
}

.control-label {
	word-wrap: break-word;
	margin-bottom: 0px;
	margin-top: 5px;
}

.checkbox {
	margin-top: 0px;
	margin-bottom: 0px;	
}

.form-control-static {
	padding-top: 0px;
	padding-bottom: 0px;
	min-height: 0px;
}

#preview-image {
	margin-top: 10px;	
}

.btn-showhide {
	background : none;
	opacity: 0.5;
}

.btn-showhide:hover, .btn-showhide:focus, .btn-showhide:active {
	background : none;
	opacity: 1;
}

/* 
 * Scroll management
 */
html, 
body, 
#main, 
.container, 
.row, 
#sideMenu, 
#sideMenu > div, 
#sideMenu > div > #sideMenuBody, 
#sideMenu > div > #sideMenuBody > #tree,
#mainContainer {
	height: 100%;, max-height: 100%; 
}

#sideMenu > div > #sideMenuBody > #tree > ul.fancytree-container {
	height: 100%;
	max-height: 100%; 
	position: relative;
	overflow-y: scroll;
	overflow-x: hidden;
}

/*
.btn {
	min-width: 45px !important;
	min-height: 37px !important;
	padding: 8px 12px 6px 14px !important;
}
*/

/*
 * Inline Form
 */
.form-inline > .form-group {
	margin-right: 10px;
}

.form-inline > .form-group > label {
	margin-right: 5px;
}

.form-inline > div > .form-group {
	margin-right: 10px;
}

.form-inline > div > .form-group > label {
	margin-right: 5px;
}

/* 
 * Login Screen 
 */
.bodyLogin {
	background: url(../img/loginBackground.jpg) no-repeat center center fixed; 
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: cover;
	
	font-family: "Helvetica Neue",sans-serif;
}

.loginForm {
	width: 450px;
	height: 450px;
	padding: 30px;
	display: block;
	
	position: absolute;
	top: 50%;
	margin-top: -280px;/* half of #content height + padding*/
	left: 50%;
	margin-left: -210px;/* half of #content height + padding*/
	
	background-color: white;
	border-radius: 60px 60px 60px 60px;
	font-family: "Helvetica Neue",sans-serif;
	font-weight: 300;
	font-size: 15px;
	
	-webkit-box-shadow: 0px 0px 100px rgba(50, 50, 50, 0.8);
	-moz-box-shadow:    0px 0px 100px rgba(50, 50, 50, 0.8);
	box-shadow:         0px 0px 100px rgba(50, 50, 50, 0.8);
}

.loginForm form {
	position: relative;
	margin: 20px 0px 30px 0px;	
}

.loginLogo {
	width: 200px;
	margin-left: auto;
	margin-right: auto;
	display: block;
	opacity:0.8;
}

.loginForm h1 {
	font-family: "Helvetica Neue", sans-serif;
	font-weight: 300;
	text-align: center;
}

.loginInputBox {
	border-bottom: 1px solid #dddddd;
}

.loginForm input {
	font-family: "Helvetica Neue",sans-serif;
	border: medium none;
	font-size: 19px;
	padding: 10px 0px 10px 10px;
	font-weight: 300;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
	width: 100%;
}

.loginForm input:focus,
.loginForm input:focus:invalid:focus {
	outline: none;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}

.loginForm .btn {
	font-family: "Helvetica Neue",sans-serif;
	left: 345px;
    position: absolute;
    top: 55px;
}

/* 
 * Navigation bar
 */
.navbar-inner {
	margin-top: 0px !important;
	-webkit-box-shadow: 6px 6px 8px rgba(50, 50, 50, 0.61);
	-moz-box-shadow:    6px 6px 8px rgba(50, 50, 50, 0.61);
	box-shadow:         6px 6px 8px rgba(50, 50, 50, 0.61);
}

/* 
 * Navigation bar - Search
 */

.navbar.navbar-default,
.nav.navbar-nav,
.nav.navbar-nav > ul {
	width: 100%;
}

.navbar-form {
	padding-left: 5px;
}

.search-query {
    padding-left: 30px;
    background-image: url(../img/search-icon.png);
    background-repeat: no-repeat;
   	background-position: 12px 50% !important;
	border-radius: 15px 15px 15px 15px;
	width: 80%;
}

.search-query-searching {
    background-image: url(../img/search-spinner.gif) !important;
    background-position: 12px 50% !important;
    background-repeat: no-repeat;
}

.ui-fancytree,
.fancytree-expanded span.fancytree-expander {
    color: #777;
    background-color: transparent !important;
    border: none !important;
}

/*
 * Sample Table
 */
.tableContainerBorder {
	margin-top:5px;
	margin-bottom:10px;
}

/* inspector */

.inspectorClicked,
.inspectorClicked:hover,
.inspectorClicked:focus  {
	background-color: #0090E3;
	background-image: none;
}

/* tool box */

.toolBox {
	margin-bottom : 10px;
}

.toolBox span {
	display: inline-block;
}

.toolBox a {
	margin-right: 4px;
}

/* filter and pagination */
.paginationTop span {
    float: left;
}

.tableFilterContainer {
	text-align:left;
}

.tableFilterContainer .search-query {
	margin-bottom: 0px;
	padding-left: 30px !important;
	background-image: url(../img/filter-icon.png);
    background-repeat: no-repeat;
    background-position: 13px 10px;
    width: 80%;
    margin-right: auto;
    margin-left: auto;
    border-radius: 15px 15px 15px 15px;
}

.paginationItem {
	margin-right: 4px;
}

.paginationTop,
.paginationBottom {
	white-space: nowrap;
	margin: 10px;
}

/* Sorting */

.interactive th:hover {
    cursor: pointer;
    background-color: #F5F5F5;
}

.current-sort {
	margin-right: 20px;
	background-color: lightblue;
	background-size:14px 14px;
	background-repeat:no-repeat;
	background-position: right center;
	border-right: 5px solid lightblue;
}

.interactive th.current-sort:hover {
	background-color: lightblue;
}

.current-sort[sort-order = DS] {
	background-image: url("../img/chevron-down-icon.png");
}

.current-sort[sort-order = AS] {
	background-image: url("../img/chevron-up-icon.png");
}

/*
 * Sample Table - Inspector Previews
 */
#sideMenu > div > .tooltip.in {
	opacity: 1;
	filter: alpha(opacity=100);
}

#sideMenu > div > .tooltip.bottom.in > .tooltip-inner {
	max-width:600px;
	background-color: transparent;
}

#sideMenu > div > .tooltip.bottom .tooltip-arrow {
    border-bottom-color: transparent;
}

#sideMenuBody > div > ul {
	/* overflow: hidden; To Fix the non overflowing tree */
	border : none;
}

/*
 * Inspector
 */
.inspectorsContainer {
	width:100%;
	margin-top: 20px;
	margin-left: 20px;
}

.inspectorsContainer div {
	float:left;
}

.inspectorLabel {
	white-space: nowrap;
}
.inspectorLineBreak {
	word-wrap: break-word;
	overflow: hidden;
	overflow-x: auto;
	width:300px;
}

div.inspectorWhiteFont {
	color: white !important;	
}

div.inspector {
	width: 500px;
	text-decoration: none;
	color: #000;
	display: block;
	overflow: hidden;
	padding: 20px;
	border-radius: 10px;
}

div.inspector > .table th,
div.inspector > .table td {
	border-top: none;
	line-height: 12px;
}

.inspectorExtra {
	margin-left: 0px !important;
}

.inspectorExtra svg {
	margin-left: auto;
	margin-right: auto;
}

.property {
  font-size: 12px;
  font-weight: normal;
}

.properties {
  padding-top:10px;
  width: 100%;	
}

.properties td:nth-child(odd) {
  /* background-color:#DDDDDD; */
  width:25%;
}

.properties td:nth-child(even) {
  /* background-color:#fbfbfb; */
  width:75%;
}

.inspectorToolbar {
opacity: 0.5;
}

.inspectorToolbar:hover {
opacity: 0.8;
}

.downloads {
	color: black;
	background-color:#DDDDDD;;
}

table.downloads {
  font-family: "Trebuchet MS", sans-serif;
	font-size: 14px;
	table-layout: fixed;
	border-collapse: collapse;
	margin: 0px;
	padding: 0px;
}

/* 
 * Inspector - Glow Effect
 */
.glow {
    animation:glow 1200ms linear 200ms 2 alternate;
    -moz-animation:glow 1200ms linear 200ms 2 alternate;
    -webkit-animation:glow 1200ms linear 200ms 2 alternate;
}

@-moz-keyframes glow { /* Firefox */
    0% {
        box-shadow:0 0 1px 1px rgba(255,255,255,0.9);
    }
    20%, 100% {
        box-shadow:0 0 1px 1px rgba(255,255,255,0.9), 0 0 3px 8px #006DCC, 0 0 2px 12px #FFF;
    }
}

@-webkit-keyframes glow { /* Safari and Chrome */
    0% {
        box-shadow:0 0 1px 1px #FFF;
    }
    20%, 100% {
        box-shadow:0 0 1px 1px #FFF, 0 0 3px 8px #006DCC, 0 0 2px 12px #FFF;
    }
}

/*
 * Sample Hierarchy
 */
 svg {
    overflow: hidden;
}

.node rect {
    /*
    stroke: #333;
    stroke-width: 1.5px;
    */
    opacity: 0;
    fill: #fff;
}

.edgeLabel rect {
    fill: #fff;
}

.edgePath {
    stroke: #333;
    stroke-width: 1.5px;
    fill: none;
}

.svgButton:hover {
	fill: #005580;
	stroke: #005580;
}

/*
 * Plate & Storage Widget
 */
.gridTable {
	border : none !important;
}
.gridTable th {
	border : none !important;
}

.gridTable td:hover {
	background-color: #E7E7E7;
	cursor : pointer;
}

.gridTable td {
	border-style:solid;
	border-width:1px;
	border-color: #CCCCCC;
}

/*
 * Plate Widget
 */
.gridTable td .well {
 	background-color: #E7E7E7;
 }
.gridTable td .well:hover {
	background-color: #C7C7C7;
	cursor : pointer;
}

.featureToolbarOption {
	max-width: 150px;
	display: inline;
}

.featureToolbarOptionSmall {
	max-width: 100px;
	display: inline;
}

.featureDropdown {
	margin: 5px;
}

/*
 * Storage Widget
 */
.storageSelectedRack {
	background-color: #CCCCCC;
}

.storageBox {
	float : left;
	background-color : #CCCCCC;
	margin : 4px;
	padding : 2px 4px;
	
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}

.storageSelectedRack .storageBox {
	background-color : #FFFFFF;
}

.rackSelected {
	background-color : #DDDDDD;
}

.storageSelectedCorner {
	background-color : #EEEEEE;
}

.hiddenInput { /* used by storage widget */
	display: none !important;
}

/*
 * Dilution Widget
 */
 .table-condensed-dilution th, .table-condensed-dilution td {
 	padding: 0 3px;
 }
 
 /*
  * Zoomable Images
  */
.zoomableImage:hover {
	cursor: pointer;
    animation:glow 1200ms linear 200ms 2 alternate;
    -moz-animation:glow 1200ms linear 200ms 2 alternate;
    -webkit-animation:glow 1200ms linear 200ms 2 alternate;
}

/*
 * Compact in-table controls
 */
.table-compact .control-group {
 	margin-bottom: 0;
}

.text-center-important {
	text-align: center !important;
}

/*
 * Side Menu Widget
 */
 
 .navbar {
 	border-radius: 0px;
 }
 
 .sideMenuTitle {
 	text-align: center;
 	padding-top:10px;
 	background-color:#F8F8F8;
 	margin-bottom:0px;
 	min-height: 40px;
	font-weight:bold;
	font-size: 16px;
	
	border-color: #E7E7E7;
	border-style:solid;
	border-width:0px 0px 1px 0px;
 }
 
 .sideMenuItem {
 	padding-left:10px;
 	padding-top:10px;
 	background-color:#FFFFFF;
 	margin-bottom:0px;
 	min-height: 40px;
 	
 	border-color: #E7E7E7;
 	border-style:solid;
	border-width:0px 0px 1px 0px;
 }
 
 .sideMenuItemTitle {
 	font-weight:bold;
 	background-color:#F8F8F8;
 }
  
 .sideMenuItemSelectable:hover {
 	background-color:#3399FF;
 	cursor: pointer;
 }
 
 .put-chevron-right {
	float:right;
	padding-right: 10px;
 }
 
 
 /*
  * Browser Compatible Links
  */
 .browser-compatible-javascript-link {
 	text-decoration: none;
 }
 
 .browser-compatible-javascript-link-tree {
 	color: #333;
 }

.browser-node {
 display: flex
}

.browser-node-icon {
 margin: auto;
 margin-right: 8px;
}

.browser-node-icon img {
 margin-left: 0
}

.browser-node-text {
 margin-right: 8px;
 flex: 1 1 auto;
 white-space: nowrap;
}

 .fancytree-active > span > a {
 	color: #FFFFFF;
 }
 
 .browser-compatible-javascript-link-menu {
 	color: #000000;
 }
 
 .browser-compatible-javascript-link-menu:hover {
 	color: #FFFFFF;
 }
 
 /*
  * Forms
  */
 .form-group {
    margin-top: 0;
    margin-bottom: 2px;
}

 legend {
    margin-top: 0;
    margin-bottom: 5px;
}

/*
 * Settgins
 */
.borderless td, .borderless th {
    border: none !important;
}

#settings-slider-low-storage, #settings-slider-low-box {
	width : 100% !important;
}

/*
 * FancyTree custom icon
 */
 
span.fancytree-custom-icon {
    margin-top: 2px;
    width: 1em;
    height: 1em;
    display: inline-block;
    vertical-align: top;
    background-repeat: no-repeat;
    background-position: 0 0;
     margin-left: .5em;
}

/* 
 * PopUp table
 */
.popup-table {
	width : 100%;
}

.popup-table th,
.popup-table td {
	padding : 4px;
	border : 1px solid #ddd;
	border-collapse: collapse;
}

/*
 *
 */

.vl {
  display: inline-block;
  border-left: 1px dashed #ddd;
  height: 30px;
  margin-left: 3px;
  vertical-align: middle;
}

/*
 * JExcel Bootstrap overrides / fixes
 */

.jexcel_container,
.jexcel_toolbar,
.jexcel_toolbar_item
{
  -webkit-box-sizing: initial !important;
  -moz-box-sizing: initial !important;
  box-sizing: initial !important;
}

.jexcel_toolbar {
    position: sticky;
    top: 0px;
    z-index : 800;
}

.jcolor-content > table > tr > td {
    padding : 7px !important;
}


.jexcel > thead > tr > td {
    z-index : auto !important;
}

/*ckeditor5 add border*/
.ck-editor__editable_inline {
    min-height: 100px;
    border-radius: 4px !important;
}

.ck-blurred {
    border: 1px solid #dedede !important;
}

/* ckeditor5 detached */

.document-editor {
    border: 1px solid var(--ck-color-base-border);
    border-radius: var(--ck-border-radius);

    /* Set vertical boundaries for the document editor. */
    max-height: 100%;

    /* This element is a flex container for easier rendering. */
    display: flex;
    flex-flow: column nowrap;
}

.document-editor__toolbar {
    /* Make sure the toolbar container is always above the editable. */
    z-index: 1;

    /* Create the illusion of the toolbar floating over the editable. */
    box-shadow: 0 0 5px hsla( 0,0%,0%,.2 );

    /* Use the CKEditor CSS variables to keep the UI consistent. */
    border-bottom: 1px solid var(--ck-color-toolbar-border);
}

/* Adjust the look of the toolbar inside the container. */
.document-editor__toolbar .ck-toolbar {
    border: 0;
    border-radius: 0;
}

/* Make the editable container look like the inside of a native word processor application. */
.document-editor__editable-container {
    padding: calc( 2 * var(--ck-spacing-large) );
    background: var(--ck-color-base-foreground);

    /* Make it possible to scroll the "page" of the edited content. */
    overflow-y: scroll;
}

.document-editor__editable-container,
.document-editor__editable-container > .ck-editor__editable {
    /* Set the dimensions of the "page". */
    width: 85%;
    /* min-height: 21cm; */

    /* Keep the "page" off the boundaries of the container. */
    padding: 1cm 2cm 2cm !important;

    border: 1px hsl( 0,0%,82.7% ) solid;
    border-radius: var(--ck-border-radius);
    background: white;

    /* The "page" should cast a slight shadow (3D illusion). */
    box-shadow: 0 0 5px hsla( 0,0%,0%,.1 );

    /* Center the "page". */
    margin: 0 auto;
}

/* Set the default font for the "page" of the content. */
.document-editor .ck-content,
.document-editor .ck-heading-dropdown .ck-list .ck-button__label {
    font: 16px/1.6 "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* Adjust the headings dropdown to host some larger heading styles. */
.document-editor .ck-heading-dropdown .ck-list .ck-button__label {
    line-height: calc( 1.7 * var(--ck-line-height-base) * var(--ck-font-size-base) );
    min-width: 6em;
}

/* Scale down all heading previews because they are way too big to be presented in the UI.
Preserve the relative scale, though. */
.document-editor .ck-heading-dropdown .ck-list .ck-button:not(.ck-heading_paragraph) .ck-button__label {
    transform: scale(0.8);
    transform-origin: left;
}

/* Set the styles for "Heading 1". */
.document-editor .ck-content h2,
.document-editor .ck-heading-dropdown .ck-heading_heading1 .ck-button__label {
    font-size: 2.18em;
    font-weight: normal;
}

.document-editor .ck-content h2 {
    line-height: 1.37em;
}

/* Set the styles for "Heading 2". */
.document-editor .ck-content h3,
.document-editor .ck-heading-dropdown .ck-heading_heading2 .ck-button__label {
    font-size: 1.75em;
    font-weight: normal;
    color: hsl( 203, 100%, 50% );
}

.document-editor .ck-heading-dropdown .ck-heading_heading2.ck-on .ck-button__label {
    color: var(--ck-color-list-button-on-text);
}

/* Set the styles for "Heading 2". */
.document-editor .ck-content h3 {
    line-height: 1.86em;
    padding-top: .171em;
    margin-bottom: .357em;
}

/* Set the styles for "Heading 3". */
.document-editor .ck-content h4,
.document-editor .ck-heading-dropdown .ck-heading_heading3 .ck-button__label {
    font-size: 1.31em;
    font-weight: bold;
}

.document-editor .ck-content h4 {
    line-height: 1.24em;
    padding-top: .286em;
    margin-bottom: .952em;
}

/* Set the styles for "Paragraph". */
.document-editor .ck-content p {
    font-size: 1em;
    margin: 0px;
}

/* Make the block quoted text serif with some additional spacing. */
.document-editor .ck-content blockquote {
    font-family: Georgia, serif;
    margin-left: calc( 2 * var(--ck-spacing-large) );
    margin-right: calc( 2 * var(--ck-spacing-large) );
}

.ck.ck-editor__editable_inline>:first-child {
     margin-top: 5px !important;
}

/* 
 * History View
 */

.history-view .changes-list ul {
    padding-inline-start: 0px; 
    list-style-type: none;
}

.history-view .changes-list ul:last-child {
    margin-bottom: 0px; 
}

.history-view .changes-list ul li {
    padding-bottom: 8px;
}

.history-view .changes-list ul li:last-child {
    padding-bottom: 0px;
}

.history-view .relation-type, .history-view .property-name {
    font-weight: bold;
}

.history-view .relation-value-removed, .history-view .content-copy-removed, .history-view .property-old-value, .history-view .relation-old-value {
    padding: 8px;
    background-color: #FBE3E4;
}

.history-view .relation-value-added, .history-view .content-copy-added, .history-view .property-new-value, .history-view .relation-new-value {
    padding: 8px;
    background-color: #E6EFC2;
}

.history-view .property-diff, .history-view .relation-diff {
    display: flex;
}

.history-view .property-old-value, .history-view .property-new-value, .history-view .relation-old-value, .history-view .relation-new-value {
    flex: 1 1 50%;
}


.history-view .full-document {
    white-space: pre-wrap; 
    min-width: 30vw;
}

ul.multiselect-container.dropdown-menu {
  max-height: 120px;
  overflow-y: auto;
}

.fa-smile-o {
  color: green;
}

.fa-frown-o {
  color: red;
}