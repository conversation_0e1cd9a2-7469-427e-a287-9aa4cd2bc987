from flask import Flask, request, jsonify
from openbis_chatbot.query.conversation_memory import ConversationMemory
from openbis_chatbot.query.query_engine import RAGQueryEngine

app = Flask(__name__)

# Initialize engines
query_engine = RAGQueryEngine()
conversation_memory = ConversationMemory()

@app.route('/openbis-chatbot/api/chat', methods=['POST'])
def chat():
    data = request.get_json()
    message = data.get('message', '')
    session_id = data.get('session_id')
    
    if not session_id:
        session_id = conversation_memory.create_session()
        return jsonify({'session_id': session_id})
    
    # Get response from query engine
    response = query_engine.get_response(message, session_id)
    
    return jsonify({
        'response': response,
        'session_id': session_id
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)