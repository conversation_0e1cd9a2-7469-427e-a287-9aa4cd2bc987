/* Chatbot Styles */
.chatbot-trigger {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #4a6fa5;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    z-index: 1000;
    transition: transform 0.3s ease;
}

.chatbot-trigger:hover {
    transform: scale(1.1);
}

.chatbot-container {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    display: none;
    z-index: 1000;
    overflow: hidden;
    flex-direction: column;
}

.chatbot-header {
    background-color: #4a6fa5;
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chatbot-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.chatbot-input {
    padding: 15px;
    border-top: 1px solid #eee;
    display: flex;
}

.chatbot-input input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 8px;
}

.chatbot-input button {
    background-color: #4a6fa5;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.chatbot-message {
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
}

.chatbot-message.user {
    align-items: flex-end;
}

.chatbot-message.assistant {
    align-items: flex-start;
}

.message-content {
    max-width: 80%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f0f0f0;
}

.chatbot-message.user .message-content {
    background-color: #4a6fa5;
    color: white;
}