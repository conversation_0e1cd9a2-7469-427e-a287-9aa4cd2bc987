var ELNDictionary = {
	Sample : "Object",
	Samples : "Objects",
	sample : "object",
	samples : "objects",
	SampleELN : "Sample",
	ExperimentELN : "Experiment",
	ExperimentCollection : "Collection",
	ExperimentsELN : "Experiments",
	ExperimentsCollection : "Collections"
}

ELNDictionary.getSampleDualName = function() {
	return ELNDictionary.SampleELN + "/" + ELNDictionary.Sample;
}

ELNDictionary.getExperimentDualName = function() {
    return ELNDictionary.ExperimentELN + "/" + ELNDictionary.ExperimentCollection;
}

ELNDictionary.getExperimentsDualName = function() {
    return ELNDictionary.ExperimentsELN + "/" + ELNDictionary.ExperimentsCollection;
}

ELNDictionary.getExperimentKindName = function(entityType, isPlural) {
    if (entityType === "COLLECTION") {
        return (isPlural) ? ELNDictionary.ExperimentsCollection : ELNDictionary.ExperimentCollection;
	} else {
		return (isPlural)?ELNDictionary.ExperimentsELN:ELNDictionary.ExperimentELN;
	}
}

ELNDictionary.settingsView = {
	sections : {
		storages : {
			title : "Storages",
			info : "Create and browse storages.",
		},
		templates : {
			title : "Templates",
			info : "Here you can edit your templates.",
		},
		mainMenu : {
			title : "Main Menu",
			info : "<br>" +
			       "<b>Space Settings:</b> showLabNotebook, showInventory, showStock and showDatasets affect the spaces of the group they are configured on, or all spaces without group if part of GENERAL_ELN_SETTINGS.<br>" +
			       "<b>Other Settings:</b> show/hide different tools of the user interface from the main menu. GENERAL_ELN_SETTINGS affect all groups.",
		},
		customWidgets : {
		    title : "Custom Widgets",
            info : "Assign custom widgets to properties!",
		},
		forcedDisableRTF : {
			title : "Forced Disable RTF",
			info : "By default all MULTILINE_VARCHAR properties have RTF. Use this section to disable the RTF on specific properties.",
		},
		forceMonospaceFont : {
			title : "Forced Monospace Font",
			info : "Use this section to force the use of monospace font for selected MULTILINE_VARCHAR properties.",
		},
		inventorySpaces : {
			title : "Inventory Spaces",
			info : "Spaces listed here are shown under the Inventory.",
		},
		dataSetTypeForFileName : {
			title : "Dataset types for filenames",
			info : "When listing a combination of file extension / Dataset Type on this section the Dataset uploader will select a Dataset Type by default. This decision can be overridden by users afterwards but provides a nice default to avoid mistakes.",
		},
		sampleTypeDefinitionsExtension : {
			title : ELNDictionary.Sample +" Type definitions Extension",
			info : "This section is used to extend the common openBIS definitions to: 1. Enable the storage. 2. Specify if the " + ELNDictionary.sample + " type is a protocol. 3. Add intended parent/children hints. 4. Support annotations for these links using properties. 5. Show in dropdowns.",
		},
		miscellaneous : {
			title : "Miscellaneous",
			info : "Miscellaneous settings."
		},
	}
}

ELNDictionary.generatedObjects = {
	searchQueriesProject: {
		description: 'Generated by ELN for stored search queries.'
	}
}