<!--
/*
 * Copyright 2014 <PERSON><PERSON>, Scientific IT Services
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN">
<html style="font-size:16px">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<title>openBIS ELN-LIMS</title>
	
	<!-- Third party Stylesheets -->
	<link type="text/css" rel="stylesheet" href="./lib/bootstrap/css/bootstrap.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/bootstrap-multiselect/css/bootstrap-multiselect.css" />
	<link type="text/css" rel="stylesheet" href="./lib/bootstrap-slider/css/bootstrap-slider.css" />
	<link type="text/css" rel="stylesheet" href="./lib/bootstrap-switch/css/toggle-switch.css" />
	<link type="text/css" rel="stylesheet" href="./lib/jquery-ui/js/jquery-ui.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/jquery-jnotify/css/jNotify.sis.jquery.css" />
	<link type="text/css" rel="stylesheet" href="./lib/jquery-tooltipster/css/tooltipster.bundle.min.css" />
    <link type="text/css" rel="stylesheet" href="./lib/jquery-tooltipster/css/plugins/tooltipster/sideTip/themes/tooltipster-sideTip-shadow.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/fuelux/css/fuelux.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/jquery-select2/css/select2.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/jquery-select2/css/select2-bootstrap.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/drawingboard/css/drawingboard.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/jquery-fancytree/js/skin-bootstrap/ui.fancytree.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/font-awesome/css/font-awesome.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/c3/c3.min.css" />
	<link type="text/css" rel="stylesheet" href="./lib/jsuites/jsuites.css" />
	<link type="text/css" rel="stylesheet" href="./lib/jexcel/jexcel.css" />
	<link type="text/css" rel="stylesheet" href="./lib/material-icons/material-icons.css" />

	<!-- ELN UI Stylesheets -->
	<link type="text/css" rel="stylesheet" href="./css/style.css" />
	<link type="text/css" rel="stylesheet" href="/openbis/resources/components/imageviewer/css/image-viewer.css" />
	<link type="text/css" rel="stylesheet" href="./css/chatbot.css" />
	<link type="text/css" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
	
	<!-- Third party libraries -->
	<script type="text/javascript" src="./lib/cycle/js/cycle.js"></script>
	<script type="text/javascript" src="./lib/jquery/js/jquery-1.11.3.min.js"></script>
	<script type="text/javascript" src="./lib/jquery/js/jquery.cookie.js"></script>
	<script type="text/javascript" src="./lib/jquery-ui/js/jquery-ui.min.js"></script>
	<script type="text/javascript" src="./lib/jquery-fancytree/js/jquery.fancytree-all.min.js"></script>
	<script type="text/javascript" src="./lib/bootstrap-datetimepicker/js/moment.js"></script>
	<script type="text/javascript" src="./lib/bootstrap/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="./lib/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
	<script type="text/javascript" src="./lib/bootstrap-multiselect/js/bootstrap-multiselect.js"></script>
	<script type="text/javascript" src="./lib/bootstrap-slider/js/bootstrap-slider.js"></script>
	<script type="text/javascript" src="./lib/bootstrap-filestyle/js/bootstrap-filestyle.min.js"></script>
	<script type="text/javascript" src="./lib/jquery-jnotify/js/jNotify.sis.jquery.js"></script>
	<script type="text/javascript" src="./lib/jquery-blockui/js/jquery.blockUI.js"></script>
	<script type="text/javascript" src="./lib/jquery-history/js/bundled/html5/jquery.history.js"></script>
	<script type="text/javascript" src="./lib/jquery-tooltipster/js/tooltipster.bundle.min.js"></script>
	<script type="text/javascript" src="./lib/jquery-tsv/js/jquery.tsv-0.96.min.js"></script>
	<script type="text/javascript" src="./lib/fuelux/js/fuelux.min.js"></script>
	<script type="text/javascript" src="./lib/jquery-select2/js/select2.full.js"></script>
	<script type="text/javascript" src="./lib/d3/js/d3.min.js"></script>
	<script type="text/javascript" src="./lib/c3/c3.min.js"></script>
	<script type="text/javascript" src="./lib/d3-dagre/js/dagre-d3.min.js"></script>
	<script type="text/javascript" src="./lib/naturalsort/js/naturalSort.js"></script>
	<script type="text/javascript" src="./lib/mozilla/js/infra.js"></script>
	<script type="text/javascript" src="./lib/filesaver/js/FileSaver.js"></script>
	<script type="text/javascript" src="./lib/drawingboard/js/drawingboard.min.js"></script>
	<script type="text/javascript" src="./lib/ckeditor/js/ckeditor.js"></script>
	<!--<script type="text/javascript" src="./lib/caja-HTML-sanitizer/js/lib/html4.js"></script>
	<script type="text/javascript" src="./lib/caja-HTML-sanitizer/js/lib/uri.js"></script>
	<script type="text/javascript" src="./lib/caja-HTML-sanitizer/js/sanitizer.js"></script>-->
	<script type="text/javascript" src="./lib/DOMPurify/purify.js"></script>
	<script type="text/javascript" src="./lib/jsuites/jsuites.js"></script>
	<script type="text/javascript" src="./lib/jexcel/jexcel.js"></script>
	<script type="text/javascript" src="./lib/bwip-js/bwipp.js"></script>
	<script type="text/javascript" src="./lib/bwip-js/bwipjs.js"></script>
	<script type="text/javascript" src="./lib/bwip-js/lib/xhr-fonts.js"></script>
	<script type="text/javascript" src="./lib/bwip-js/lib/bitmap.js"></script>
	<script type="text/javascript" src="./lib/bwip-js/lib/symdesc.js"></script>
	<script type="text/javascript" src="./lib/bwip-js/lib/canvas-toblob.js"></script>
	<script type="text/javascript" src="./lib/jspdf/js/jspdf.min.js"></script>
	<script type="text/javascript" src="./lib/zxing-js/js/zxing-js-min.js"></script>

	<script type="text/javascript" src="./lib/react/react.production.min.js"></script>
	<script type="text/javascript" src="./lib/react/react-dom.production.min.js"></script>
	<script type="text/javascript" src="./lib/react/Components.js"></script>

	<!-- First party libraries -->
	<script type="text/javascript" src="../../resources/js/openbis.js"></script>
	<script type="module" src="../../resources/js/openbis.js"></script>
	<script type="text/javascript" src="./js/config/chatbot-config.js"></script>
	<script type="text/javascript" src="./js/chatbot-init.js"></script>
	<script type="text/javascript">
		// Defaults to override by config
		var PLUGINS_CONFIGURATION = {
		    extraPlugins : ["life-sciences", "flow", "microscopy"]
		};

		var options = {
    		showResearchCollectionExportBuilder: false
		};
	</script>
	<script type="text/javascript" src="../../resources/api/v3/config.js"></script>
	<script type="text/javascript" src="../../resources/api/v3/require.js"></script>
	<script type="text/javascript" src="./lib/grid/js/Grid.js"></script>

	<!-- ELN UI -->
	<script type="text/javascript" src="./js/controllers/LayoutManager.js"></script>
	<script type="text/javascript" src="./js/controllers/MainController.js"></script>
	
	<script type="text/javascript" src="./js/util/IdentifierUtil.js"></script>
	<script type="text/javascript" src="./js/config/ELNDictionary.js"></script>
	<script type="text/javascript" src="./js/config/Profile.js"></script>
	<script type="text/javascript" src="./js/config/StandardProfile.js"></script>
	<script type="text/javascript" src="./js/config/SettingsManager.js"></script>
	<script type="text/javascript" src="./js/server/ServerFacade.js"></script>
	
	<script type="text/javascript" src="./js/util/Util.js"></script>
	<script type="text/javascript" src="./js/util/ExportUtil.js"></script>
	<script type="text/javascript" src="./js/util/FormUtil.js"></script>
	<script type="text/javascript" src="./js/util/Select2Manager.js"></script>
	<script type="text/javascript" src="./js/util/CKEditorManager.js"></script>
	<script type="text/javascript" src="./js/util/JExcelEditorManager.js"></script>
	<script type="text/javascript" src="./js/util/PrintUtil.js"></script>
	<script type="text/javascript" src="./js/util/TreeUtil.js"></script>
	<script type="text/javascript" src="./js/util/JupyterUtil.js"></script>
	<script type="text/javascript" src="./js/util/AnnotationUtil.js"></script>
	<script type="text/javascript" src="./js/util/HierarchyUtil.js"></script>
	<script type="text/javascript" src="./js/util/BarcodeUtil.js"></script>
	
	<script type="text/javascript" src="./js/views/TrashManager/TrashManagerController.js"></script>
	<script type="text/javascript" src="./js/views/TrashManager/TrashManagerModel.js"></script>
	<script type="text/javascript" src="./js/views/TrashManager/TrashManagerView.js"></script>
	
	<script type="text/javascript" src="./js/views/StorageManager/StorageManagerController.js"></script>
	<script type="text/javascript" src="./js/views/StorageManager/StorageManagerModel.js"></script>
	<script type="text/javascript" src="./js/views/StorageManager/StorageManagerView.js"></script>
	<script type="text/javascript" src="./js/views/StorageManager/widgets/StorageController.js"></script>
	<script type="text/javascript" src="./js/views/StorageManager/widgets/StorageModel.js"></script>
	<script type="text/javascript" src="./js/views/StorageManager/widgets/StorageView.js"></script>
	<script type="text/javascript" src="./js/views/StorageManager/widgets/GridController.js"></script>
	<script type="text/javascript" src="./js/views/StorageManager/widgets/GridModel.js"></script>
	<script type="text/javascript" src="./js/views/StorageManager/widgets/GridView.js"></script>

	<script type="text/javascript" src="./js/views/DataGrid/DataGridExportOptions.js"></script>
	<script type="text/javascript" src="./js/views/DataGrid/DataGridController.js"></script>
	<script type="text/javascript" src="./js/views/DataGrid/SampleDataGridUtil.js"></script>

	<script type="text/javascript" src="./js/views/SampleForm/SampleFormController.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/SampleFormModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/SampleFormView.js"></script>
	
	<script type="text/javascript" src="./js/views/AdvancedSearch/AdvancedSearchController.js"></script>
	<script type="text/javascript" src="./js/views/AdvancedSearch/AdvancedSearchModel.js"></script>
	<script type="text/javascript" src="./js/views/AdvancedSearch/AdvancedSearchView.js"></script>
	
	<script type="text/javascript" src="./js/views/SampleForm/widgets/StorageListController.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/StorageListModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/StorageListView.js"></script>
	
	<script type="text/javascript" src="./js/views/SampleForm/widgets/DilutionTableController.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/DilutionTableModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/DilutionTableView.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/FreeFormTableController.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/FreeFormTableModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/FreeFormTableView.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/DeleteEntityController.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/DeleteEntityModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/DeleteEntityView.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/CommentsController.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/CommentsModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/CommentsView.js"></script>
	
	<script type="text/javascript" src="./js/views/SampleForm/widgets/ordering/NewProductsController.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/ordering/NewProductsModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/ordering/NewProductsView.js"></script>
	
	<script type="text/javascript" src="./js/views/SampleForm/widgets/LinksController.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/LinksModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/LinksView.js"></script>
	<script type="text/javascript" src="./js/views/SampleForm/widgets/SampleField.js"></script>

	<script type="text/javascript" src="./js/views/SpaceForm/SpaceFormController.js"></script>
	<script type="text/javascript" src="./js/views/SpaceForm/SpaceFormModel.js"></script>
	<script type="text/javascript" src="./js/views/SpaceForm/SpaceFormView.js"></script>
	
	<script type="text/javascript" src="./js/views/ProjectForm/ProjectFormController.js"></script>
	<script type="text/javascript" src="./js/views/ProjectForm/ProjectFormModel.js"></script>
	<script type="text/javascript" src="./js/views/ProjectForm/ProjectFormView.js"></script>
	
	<script type="text/javascript" src="./js/views/ExperimentForm/ExperimentFormController.js"></script>
	<script type="text/javascript" src="./js/views/ExperimentForm/ExperimentFormModel.js"></script>
	<script type="text/javascript" src="./js/views/ExperimentForm/ExperimentFormView.js"></script>
	
	<script type="text/javascript" src="./js/views/DropboxMonitor/DropboxMonitorController.js"></script>
	<script type="text/javascript" src="./js/views/DropboxMonitor/DropboxMonitorModel.js"></script>
	<script type="text/javascript" src="./js/views/DropboxMonitor/DropboxMonitorView.js"></script>
	<script type="text/javascript" src="./js/views/DropboxMonitor/DropboxMonitorUtil.js"></script>
	<script type="text/javascript" src="./js/views/DropboxMonitor/modal/DropboxLogsController.js"></script>
	<script type="text/javascript" src="./js/views/DropboxMonitor/modal/DropboxLogsModel.js"></script>
	<script type="text/javascript" src="./js/views/DropboxMonitor/modal/DropboxLogsView.js"></script>
	<script type="text/javascript" src="./js/views/ArchivingHelper/ArchivingHelperController.js"></script>
	<script type="text/javascript" src="./js/views/ArchivingHelper/ArchivingHelperModel.js"></script>
	<script type="text/javascript" src="./js/views/ArchivingHelper/ArchivingHelperView.js"></script>
	<script type="text/javascript" src="./js/views/UnarchivingHelper/UnarchivingHelperController.js"></script>
	<script type="text/javascript" src="./js/views/UnarchivingHelper/UnarchivingHelperModel.js"></script>
	<script type="text/javascript" src="./js/views/UnarchivingHelper/UnarchivingHelperView.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/UserManagerController.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/UserManagerModel.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/UserManagerView.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/modal/CreateUserController.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/modal/CreateUserModel.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/modal/CreateUserView.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/modal/ResetPasswordController.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/modal/ResetPasswordModel.js"></script>
	<script type="text/javascript" src="./js/views/UserManager/modal/ResetPasswordView.js"></script>
    <script type="text/javascript" src="./js/views/UserManagementConfig/UserManagementConfigController.js"></script>
    <script type="text/javascript" src="./js/views/UserManagementConfig/UserManagementConfigModel.js"></script>
    <script type="text/javascript" src="./js/views/UserManagementConfig/UserManagementConfigView.js"></script>
	
    <script type="text/javascript" src="./js/views/CustomImport/CustomImportController.js"></script>
    <script type="text/javascript" src="./js/views/CustomImport/CustomImportModel.js"></script>
    <script type="text/javascript" src="./js/views/CustomImport/CustomImportView.js"></script>

	<script type="text/javascript" src="./js/views/DataSetForm/DataSetFormController.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/DataSetFormModel.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/DataSetFormView.js"></script>
	
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/AdvancedEntitySearchDropdown.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/DatasetViewerController.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/DatasetViewerModel.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/DatasetViewerView.js"></script>
	
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/JupyterNotebookController.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/JupyterNotebookModel.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/JupyterNotebookView.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/JupyterCopyNotebookController.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/JupyterCopyNotebookModel.js"></script>
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/JupyterCopyNotebookView.js"></script>
	
	<script type="text/javascript" src="./js/views/DataSetForm/widgets/ImagePreviewIconLoader.js"></script>
	
	<script type="text/javascript" src="./js/views/SideMenu/SideMenuWidgetController.js"></script>
	<script type="text/javascript" src="./js/views/SideMenu/SideMenuWidgetBrowserController.js"></script>
	<script type="text/javascript" src="./js/views/SideMenu/SideMenuWidgetModel.js"></script>
	<script type="text/javascript" src="./js/views/SideMenu/SideMenuWidgetView.js"></script>
	
	<script type="text/javascript" src="./js/views/VocabularyManager/VocabularyManagerController.js"></script>
	<script type="text/javascript" src="./js/views/VocabularyManager/VocabularyManagerModel.js"></script>
	<script type="text/javascript" src="./js/views/VocabularyManager/VocabularyManagerView.js"></script>
	
	<script type="text/javascript" src="./js/views/HierarchyTable/HierarchyTableController.js"></script>
	<script type="text/javascript" src="./js/views/HierarchyTable/HierarchyTableModel.js"></script>
	<script type="text/javascript" src="./js/views/HierarchyTable/HierarchyTableView.js"></script>
	<script type="text/javascript" src="./js/views/HierarchyTable/widgets/HierarchyFilterController.js"></script>
	<script type="text/javascript" src="./js/views/HierarchyTable/widgets/HierarchyFilterModel.js"></script>
	<script type="text/javascript" src="./js/views/HierarchyTable/widgets/HierarchyFilterView.js"></script>

	<script type="text/javascript" src="./js/views/History/HistoryController.js"></script>
	<script type="text/javascript" src="./js/views/History/HistoryModel.js"></script>
	<script type="text/javascript" src="./js/views/History/HistoryView.js"></script>

	<script type="text/javascript" src="./js/views/SampleTable/SampleTableController.js"></script>
	<script type="text/javascript" src="./js/views/SampleTable/SampleTableModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleTable/SampleTableView.js"></script>
    <script type="text/javascript" src="./js/views/SampleTable/widgets/BatchController.js"></script>
    <script type="text/javascript" src="./js/views/SampleTable/widgets/BatchModel.js"></script>
    <script type="text/javascript" src="./js/views/SampleTable/widgets/BatchView.js"></script>
	<script type="text/javascript" src="./js/views/SampleTable/widgets/TypeAndFileController.js"></script>
	<script type="text/javascript" src="./js/views/SampleTable/widgets/TypeAndFileModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleTable/widgets/TypeAndFileView.js"></script>
	<script type="text/javascript" src="./js/views/SampleTable/widgets/MoveSampleController.js"></script>
	<script type="text/javascript" src="./js/views/SampleTable/widgets/MoveSampleModel.js"></script>
	<script type="text/javascript" src="./js/views/SampleTable/widgets/MoveSampleView.js"></script>
	
	<script type="text/javascript" src="./js/views/legacy/SampleHierarchy.js"></script>
	
	<script type="text/javascript" src="./js/views/LabNotebook/LabNotebookController.js"></script>
	<script type="text/javascript" src="./js/views/LabNotebook/LabNotebookModel.js"></script>
	<script type="text/javascript" src="./js/views/LabNotebook/LabNotebookView.js"></script>
	
	<script type="text/javascript" src="./js/views/SettingsForm/SettingsFormController.js"></script>
	<script type="text/javascript" src="./js/views/SettingsForm/SettingsFormModel.js"></script>
	<script type="text/javascript" src="./js/views/SettingsForm/SettingsFormView.js"></script>
	
	<script type="text/javascript" src="./js/views/Inventory/InventoryController.js"></script>
	<script type="text/javascript" src="./js/views/Inventory/InventoryModel.js"></script>
	<script type="text/javascript" src="./js/views/Inventory/InventoryView.js"></script>
	
	<script type="text/javascript" src="./js/views/Stock/StockController.js"></script>
	<script type="text/javascript" src="./js/views/Stock/StockModel.js"></script>
	<script type="text/javascript" src="./js/views/Stock/StockView.js"></script>

	<script type="text/javascript" src="./js/views/UserProfile/UserProfileController.js"></script>
	<script type="text/javascript" src="./js/views/UserProfile/UserProfileModel.js"></script>
	<script type="text/javascript" src="./js/views/UserProfile/UserProfileView.js"></script>

	<script type="text/javascript" src="./js/views/Export/ExportTreeController.js"></script>
	<script type="text/javascript" src="./js/views/Export/ExportTreeModel.js"></script>
	<script type="text/javascript" src="./js/views/Export/ExportTreeView.js"></script>
	
	<script type="text/javascript" src="./js/views/ResearchCollectionExport/ResearchCollectionExportController.js"></script>
	<script type="text/javascript" src="./js/views/ResearchCollectionExport/ResearchCollectionExportModel.js"></script>
	<script type="text/javascript" src="./js/views/ResearchCollectionExport/ResearchCollectionExportView.js"></script>

	<script type="text/javascript" src="./js/views/ZenodoExport/ZenodoExportController.js"></script>
	<script type="text/javascript" src="./js/views/ZenodoExport/ZenodoExportModel.js"></script>
	<script type="text/javascript" src="./js/views/ZenodoExport/ZenodoExportView.js"></script>

	<script type="text/javascript" src="./js/views/DrawingBoards/DrawingBoardsController.js"></script>
	<script type="text/javascript" src="./js/views/DrawingBoards/DrawingBoardsModel.js"></script>
	<script type="text/javascript" src="./js/views/DrawingBoards/DrawingBoardsView.js"></script>
	
	<script type="text/javascript" src="./js/views/Shared/widgets/MoveEntityController.js"></script>
	<script type="text/javascript" src="./js/views/Shared/widgets/MoveEntityModel.js"></script>
	<script type="text/javascript" src="./js/views/Shared/widgets/MoveEntityView.js"></script>
	
	<script type="text/javascript" src="./js/views/DataGrid/ExperimentDataGridUtil.js"></script>
	<script type="text/javascript" src="./js/views/ExperimentTable/ExperimentTableController.js"></script>
	<script type="text/javascript" src="./js/views/ExperimentTable/ExperimentTableModel.js"></script>
	<script type="text/javascript" src="./js/views/ExperimentTable/ExperimentTableView.js"></script>
	
	<script type="text/javascript" src="./js/config/ELNLIMSPlugin.js"></script>
	<script type="text/javascript" src="./plugins/generic/plugin.js"></script>

	<script type="text/javascript" src="./js/util/EventUtil.js"></script>
	<script type="text/javascript" src="./js/test/TestUtil.js"></script>
	<script type="text/javascript" src="./js/test/UserTests.js"></script>
	<script type="text/javascript" src="./js/test/AdminTests.js"></script>
	<script type="text/javascript" src="./js/test/TestProtocol.js"></script>
	<script type="text/javascript" src="./js/test/ReactTestUtils.js"></script>

	<script type="text/javascript">
	
	//
	// Application Startup
	//
    window.addEventListener('popstate', function(event) {
        if (sessionStorage.getItem("normalLoginHasBeenForces") == "true") {
            location.reload();
        }
    });
	var profile = null;
	var mainController = null;	
	var startELNLIMS = function() {
		mainController = new MainController(profile);
		mainController.initAppBeforeLogin(function(){
			// Global links handler - This function avoid normal link behaviour for javascript enabled links that will use ajax calls for left clicks, allowing to open them on a new tab with right click.
			$(document).click(function(e) {
				var elementClasses = $(e.target).attr('class');
				var isLeftClick = e.which === 1;
				if(isLeftClick && elementClasses && elementClasses.indexOf("browser-compatible-javascript-link") !== -1) {
					e.preventDefault();
					e.stopPropagation();
				}
			});
			
			$(document).ajaxError(function( event, jqxhr, settings, thrownError ) {
				try {
					Util.showError("AJAX Error status: " + jqxhr.status + " - Status text: " + jqxhr.statusText + " - Calling: " + settings.url);
				} catch(err) {
					Util.showError("Unknown AJAX Error");
				}
			});
			
			$('#main').hide();
			var username = $("#username").value;
			if(username == null || username.length==0) {
				$("#username").focus();
			} else {
				$("#login-button").focus();
			}
		
			$('#openbis-login-service').submit(function() {
				Util.blockUI();
				$("#mainContainer").show();
				var username = $('#username').val();
				var password = $('#password').val();
				mainController.serverFacade.login(
						$.trim($('#username').val()), 
						$.trim($('#password').val()), 
						function(data) { mainController.enterApp(data, username, password) });
			});
			var singleSignOnUrlTemplate = mainController.profile.singleSignOnUrlTemplate;
			if (singleSignOnUrlTemplate) { // Single sign on login service enabled
				// Hide the openbis login service
				var $openbisLoginService = $("#openbis-login-service")
				$openbisLoginService.css('margin-top', '0px');
				$openbisLoginService.css('margin-bottom', '6px');
				$openbisLoginService.hide();

				// Show the single sign on login service
				var $container = $("#single-sign-on-login-service");
				$container.empty();
				$container.css('text-align', 'center');
				$container.css('min-height', '62px');
				$container.css('margin-top', '20px');
				$container.css('margin-bottom', '20px');
				
				var url = mainController.profile.singleSignOnUrlTemplate
				var url = url.replaceAll("${host}", window.location.hostname);
				var url = url.replaceAll("${current-url}", window.location.href);

				var link = $("<a>", {href: url, text: "Login"})
				link.css('font-weight', 'bold')
				link.css('font-size', '18px')
				$container.append(link);
				$container.show();

				// Show login selector
				var $loginServicesContainer = $("#login-service-selector");
				var $loginServices = FormUtil.getDropdown([
					{ label: mainController.profile.singleSignOnLinkLabel, value: 'SSO', selected: true },
					{ label: 'Default Login Service', value: 'OPENBIS' }
				]);
				$loginServices.change(function() {
					$("#single-sign-on-login-service").hide();
					$("#openbis-login-service").hide();

					switch($loginServices.val()) {
						case 'SSO':
								$("#single-sign-on-login-service").show();
							break;
						case 'OPENBIS':
								$("#openbis-login-service").show();
							break;
					}
				});
				$loginServicesContainer.append($loginServices);

			} else { // Only openbis login service enabled
				// Do nothing
			}

			var queryString = Util.queryString();
			var test = queryString.test;
			var testWithLogin = queryString.testWithLogin;

			if (test == "true" || testWithLogin == "true" || $.cookie("suitename")) {
				sessionStorage.setItem("forceNormalLogin", "true");
			}

			mainController.serverFacade.ifRestoredSessionActive(function(data) { mainController.enterApp(data) });
			if (sessionStorage.getItem("forceNormalLogin") != "true" && sessionStorage.getItem("loggedInAnonymously") != "false") {
				mainController.serverFacade.getOpenbisV3(function(openbisV3) {
					openbisV3.loginAsAnonymousUser().done(function(sessionToken) {
						mainController.serverFacade.openbisServer.useSession(sessionToken);
						mainController.serverFacade.openbisServer.rememberSession();
						mainController.loggedInAnonymously = true;
						sessionStorage.setItem("loggedInAnonymously", "true");
						mainController.enterApp({"result":true});
					});
				});
			} else {
				sessionStorage.setItem("normalLoginHasBeenForces", "true");
				sessionStorage.setItem("loggedInAnonymously", "false");
			}
			sessionStorage.removeItem("forceNormalLogin");

			
			// Make the ENTER key the default button
			$("openbis-login-service input").keypress(function (e) {
				if ((e.which && e.which == 13) || (e.keyCode && e.keyCode == 13)) {
					$('button[type=submit].default').click();
					return false;
				} else {
					return true;
				}
			});
			
			//Automatic login if special parameters are given
			var user = queryString.user;
			var pass = queryString.pass;
			if(user && pass) {
				Util.blockUI();
				mainController.serverFacade.login(user, pass, function(data) { mainController.enterApp(data) });
			}
			//Reset password
			if (mainController.resetPasswordRequested()) {
				mainController.resetPassword();
			}

			var jenkins = queryString.jenkins;
			if (jenkins == "true") {
				$.cookie("report-to-jenkins", "true");
			}

			if (testWithLogin == "true") {
				TestProtocol.startAdminTests(true);
			} else if (test == "true") {
				TestProtocol.startAdminTests(false);
			}

			if ($.cookie("suitename") == "testId") {
				TestProtocol.startUserTests();
			}

			if ($.cookie("suitename") == "finishTest") {
				TestProtocol.finishTests();
			}
		})
	}

	$(document).ready(function() {
		var numPlugins = !PLUGINS_CONFIGURATION?1:1+PLUGINS_CONFIGURATION.extraPlugins.length;
		var pluginsOnLoad = false;

		var wait = null;
			wait = function() {
				if (!profile) {
					setTimeout(wait,100);
				} else {
					if(profile.plugins.length === numPlugins) {
						startELNLIMS();
					} else if(!pluginsOnLoad) {
						if(PLUGINS_CONFIGURATION && PLUGINS_CONFIGURATION.extraPlugins) {
							for(var pIdx = 0; pIdx < PLUGINS_CONFIGURATION.extraPlugins.length; pIdx++) {
								loadJSResorce("./plugins/" + PLUGINS_CONFIGURATION.extraPlugins[pIdx] + "/plugin.js");
							}
						}
						pluginsOnLoad = true;
						setTimeout(wait,100);
					} else {
						setTimeout(wait,100);
					}
				}
			}
			wait();
	});
	</script>
</head>
<body class="bodyLogin">
	<div id="login-form-div" class="loginForm" visibility="hidden">
			<img id="mainLogo" class="loginLogo" src="" alt="openBIS" />
			<h1 id="mainLogoTitle"></h1>

			<div id="login-wrapper">
				<div id="login-service-selector"></div>
				<div id="single-sign-on-login-service"></div>
				<form id="openbis-login-service" action="javascript:">
					<fieldset>
							<div class='loginInputBox'>
								<input placeholder="Account" id="username" type="text" required="required">
							</div>

							<div class='loginInputBox'>
								<input placeholder="Password" id="password" type="password" required="required">
							</div>

							<button class="btn btn-default" id="login-button" type="submit"><span class="glyphicon glyphicon-arrow-right"></span></button>
					</fieldset>
				</form>
			</div>

			<center>
				<div id="password-reset-container"></div>
				<div id="help"></div>
				<div style="margin-bottom: 5px;">Compatible With:</div>
				<img src="./img/browser-icon-chrome.png" style="width: 43px; height:43px;" /><img src="./img/browser-icon-safari.png" style="width: 43px; height:43px;" /><img src="./img/browser-icon-firefox.png" style="width: 43px; height:43px;" />
			</center>
	</div>

	<div class="container-fluid">
		<div id='mainContainer' class="row"></div>
	</div>

	<script type="text/javascript" src="./etc/config.js"></script>

</body>
</html>