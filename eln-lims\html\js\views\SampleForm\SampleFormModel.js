/*
 * Copyright 2014 <PERSON><PERSON>, Scientific IT Services
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

function SampleFormModel(mode, sample, paginationInfo) {
	this.mode = mode;
	this.sample = sample;
	this.datasets = [];

	this.views = null;

	this.isFormDirty = false;
	this.isFormLoaded = false;
	this.isELNSample = !profile.isInventorySpace(sample.spaceCode);

	this.sampleType = null;

	//
	// TO-DO: Legacy code to be refactored
	//
	this.storages = [];
	this.dataSetViewer = null;
	this.sampleLinksParents = EmptyLinksController;
	this.sampleLinksChildren = EmptyLinksController;
	this.sampleLinksNone = null;
	this.paginationInfo = paginationInfo;
}