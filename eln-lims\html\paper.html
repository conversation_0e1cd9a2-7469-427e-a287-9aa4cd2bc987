<!--
/*
 * Copyright 2014 <PERSON><PERSON>, Scientific IT Services
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
<title>Publication</title>

    <link type="text/css" rel="stylesheet" href="./lib/bootstrap/css/bootstrap.min.css" />
    <script type="text/javascript" src="./lib/jquery/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="./lib/bootstrap/js/bootstrap.min.js"></script>

<style>
p {
	text-align: justify;
}
/* title */
.header {
	margin-top: 20px;
	padding: 40px;
	background: lightgrey;
}
/* groups */
.group .subgroup {
	background: orange;
	width: 150px;
	height: 200px;
}

.fixed {
	position: fixed;
}

/* sidebar */
.bs-docs-sidebar {
	padding-left: 20px;
	margin-top: 20px;
	margin-bottom: 20px;
}

/* all links */
.bs-docs-sidebar .nav>li>a {
	color: #999;
	border-left: 2px solid transparent;
	padding: 4px 20px;
	font-size: 13px;
	font-weight: 400;
}

/* nested links */
.bs-docs-sidebar .nav .nav>li>a {
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 30px;
	font-size: 12px;
}

/* active & hover links */
.bs-docs-sidebar .nav>.active>a,.bs-docs-sidebar .nav>li>a:hover,.bs-docs-sidebar .nav>li>a:focus
	{
	color: grey;
	text-decoration: none;
	background-color: transparent;
	border-left-color: lightgrey;
}
/* all active links */
.bs-docs-sidebar .nav>.active>a,.bs-docs-sidebar .nav>.active:hover>a,.bs-docs-sidebar .nav>.active:focus>a
	{
	font-weight: 700;
}
/* nested active links */
.bs-docs-sidebar .nav .nav>.active>a,.bs-docs-sidebar .nav .nav>.active:hover>a,.bs-docs-sidebar .nav .nav>.active:focus>a
	{
	font-weight: 500;
}

.col-xs-9 {
	padding-left: 55px;
    padding-bottom: 50px;
}
    
.header .btn {
    width: 15em;
}
</style>

</head>
<body>
	<div class="container-fluid">
		<div class="row header">
			<div class="col-xs-12">
				<h1>openBIS ELN-LIMS: an open-source database for academic laboratories</h1>
				<p>An easy to use, intuitive interface for daily laboratory operations built on top of modern web technologies.</p>
				<p>
					<a class="btn btn-default" href="./?user=testuser&pass=openbis" target="_blank" role="button">Demo</a> <a class="btn btn-default"
						href="https://wiki-bsse.ethz.ch/display/bis/openBIS+Download+Page" target="_blank">Download</a> <a class="btn btn-default" href="https://wiki-bsse.ethz.ch/display/openBISDoc/openBIS+ELN-LIMS"
						target="_blank">Documentation</a>
				</p>
			</div>
		</div>
	</div>
	<div class="container-fluid">
		<div class="row">
			<!--Main Content -->
			<div class="col-xs-9">

				<h1 id="Overview">Overview</h1>

				<h3 id="Motivation">Motivation</h3>

				<p>Reproducibility across different “generations” of students in an academic lab is harder to achieve than most people wish. One of the main reasons for this is poor documentation of previous
					work. Nowadays most data is in electronic format, but the associated experimental details are still reported in paper notebooks. Moreover, used material and methods are spread over several
					different spreadsheets, text documents or databases and it is difficult to put all this information together.</p>

				<h3 id="Goal">Goal</h3>
				<p>The aim of openBIS ELN-LIMS was to create a stable database with an intuitive and simple-to-use graphical user interface (GUI) where all relevant lab data can be collected. To this end, we
					built upon openBIS, a database aimed for long lasting storage of large amount of data. We introduced a LIMS (where the data associated with the material and methods are stored) and an ELN (where
					the experimental observations are stored) part. openBIS ELN-LIMS supports everyday work in the laboratory by making the data immediately accessible and easy to find.</p>

				<h3 id="Development">Development</h3>

				<p>The software platform openBIS was developed over the course of the last 7 years. The extension of the platform to accommodate the ELN and LIMS was a collaboration between a small
					experimental group and the then departmental scientific IT services group. For the last 2 years the software was tested by several research groups of the ETH and the feedback from testing the
					system was incorporated to make the ELN-LIMS ready for everyday use in the lab. The software is now supported by 2 departments (BIOL and BSSE) of the ETH and is in use in several labs.</p>

				<h1 id="Support">Support</h1>
				<p>Data associated with an ELN-LIMS need to be available for a long time. This is first ensured by the flexible structure of the database. Second, we took great care that the maintenance,
                    support and further development of the software is secured. The group developing and maintaining the database is now part of the <a href=" https://sis.id.ethz.ch" target="_blank">Scientific IT Services (SIS) division</a>, a core infrastructure unit
					of ETH Zurich. SIS has software developers and domain specialists for testing and supporting the database in the labs. The group is devoted to following best practices for software development
					like version control, unit and system testing, continuous integration and code reviews. A high standard of software engineering is ensured which is independent of any individual software
                    developer.</p>
                    
                    <p>The current version of openBIS ELN-LIMS is stable and used by several groups productively. However, software like this is not static and we are committed to develop the software further based on feedback
					by our users. Priority will be given to such feature requests which are useful to many users.</p>

				<h1 id="DemoInstanceDescription">Demo Instance Description</h1>

				<h3 id="Introduction">Introduction</h3>
				<p>This instance demonstrates how openBIS ELN-LIMS can be used to record, manage, and retrieve data produced in a typical academic bio-molecular laboratory. To illustrate the concept we used
					some of our recently published data on inducible transcription factors in yeast (Ottoz et al., Nucleic Acids Research, 2014).</p>

				<h3 id="LIMS">LIMS</h3>
				<p>The materials and methods used for this work were entered in the Inventory section as Samples. This section contains two Data Spaces: MATERIALS and METHODS. The hierarchical structure of
					openBIS allows us to organize the materials in several categories: PLASMIDS, POLYNUCLEOTIDES, REAGENTS (including ANTIBODIES, CHEMICALS, SOLUTIONS_BUFFERS etc.), and YEASTS. Similarly, we
					organized the methods in two groups: GENERAL_PROTOCOLS and WESTERN_BLOTTING_PROTOCOLS.</p>
				<p>All information stored in a Sample, including the attached Datasets, can be visualized by clicking on it. Parent-child relationships illustrate the Sample’s context in the database. For
					example, the parents of the SOLUTIONS_BUFFERS Samples are the CHEMICALS or SOLUTIONS_BUFFERS to be mixed together. To specify the nature of these relationships, we annotate the links with the
					needed quantities.</p>

				<h3 id="ELN">ELN</h3>
				<p>The experimental results are organized and stored in the Lab Notebook section. Here, we created a dedicated Data Space called DIANA_OTTOZ. Within this Data Space, we created the
					INDUCIBLE_TRANSCRIPTION_FACTOR Project where we organized the work in Experiments. In the Lab Notebook section, each Experiment represents a scientific question where the experimental goals and
					outcomes are briefly described. We created two Experiments: "Induction of the transcription factor in standard growth conditions with synthetic complete medium containing 2% of glucose", where
					the goal was to analyze the induction of the transcription factor in different concentrations of inducer using flow cytometry and western blotting; "Analysis of the abundance of the four variants
					of the transcription factor before and after induction", where the goal was to analyze the correlation between the transcription factor abundance and the reached induction levels using western
					blotting. Within each Experiment, we listed the bench experiments performed to answer the scientific question as Experimental Steps. For example, "Induction of the transcription factor in
					standard growth conditions with synthetic complete medium containing 2% of glucose" contains three Experimental steps: "Detection of LexA-ER-B42 induction by flow cytometry", "Detection of
					LexA-ER-B42 induction by western blotting", and "Detection of LexA-ER-B112 induction by western blotting".</p>
				<p>The Sample "Detection of LexA-ER-B42 induction by western blotting" describes a western blotting performed to assess the induction levels of the transcription factor variant called
					LexA-ER-B42. Materials and methods Samples used to obtain these data are listed as Parents. The annotation of each of these relationships gives practical information about how the particular
					experiment was performed, and is necessary to reproduce the results. Additional information, like the readout details and the general comments on the outcome of the experiment, is also contained
					in the Sample. The actual results, the western blot images, are uploaded as Datasets. A table describes the order of the protein extracts loaded on each lane of the gel, allowing one to fully
					understand the western blot images.</p>

			</div>

			<!--Nav Bar -->
			<nav class="col-xs-3 bs-docs-sidebar">
			<ul id="sidebar" class="nav nav-stacked fixed">
				<li><a href="#Overview">Overview</a>
					<ul class="nav">
						<li><a href="#Motivation">Motivation</a></li>
						<li><a href="#Goal">Goal</a></li>
						<li><a href="#Development">Development</a></li>
					</ul></li>
				<li><a href="#Support">Support</a></li>
				<li><a href="#DemoInstanceDescription">Demo Instance Description</a>
					<ul class="nav">
						<li><a href="#Introduction">Introduction</a></li>
						<li><a href="#LIMS">LIMS</a></li>
						<li><a href="#ELN">ELN</a></li>
					</ul></li>

			</ul>
			</nav>
		</div>
	</div>
</body>
</html>
