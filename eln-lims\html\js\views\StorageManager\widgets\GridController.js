/*
 * Copyright 2014 <PERSON><PERSON>, Scientific IT Services
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

function GridController(isSelectMultiple, isDragable, gridId) {
	this._gridModel = new GridModel(isSelectMultiple, isDragable, gridId);
	this._gridView = new GridView(this._gridModel);
	
	this.init = function($container) {
		this._gridView.repaint($container);
	}
	
	//
	// Getters
	//
	this.getModel = function() {
		return this._gridModel;
	}
	
	this.getView = function() {
		return this._gridView;
	}
	
	//
	// API
	//
	this.selectPosition = function(posX, posY, label) {
		this._gridView._selectPosition(posX, posY, label);
	}
}